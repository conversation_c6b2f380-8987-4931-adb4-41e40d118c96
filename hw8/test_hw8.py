from homework8 import *

# print('*'*100)
# print(ngrams(1, ["a", "b", "c"]))
# print('*'*100)
# print(ngrams(2, ["a", "b", "c"]))
# print('*'*100)
# print(ngrams(3, ["a", "b", "c"]))
# print('*'*100)
# m = NgramModel(1)
# m.update("a b c d")
# m.update("a b a b")
# random.seed(1)
# print([m.random_token(()) for i in range(25)])
# print('*'*100)
# m = NgramModel(2)
# m.update("a b c d")
# m.update("a b a b")
# random.seed(2)
# print([m.random_token(("<START>",)) for i in range(6)])
# print('*'*100)
# print([m.random_token(("b",)) for i in range(6)])
# print('*'*100)
# m = NgramModel(1)
# m.update("a b c d")
# m.update("a b a b")
# random.seed(1)
# print(m.random_text(13))
# print('*'*100)
# m = NgramModel(2)
# m.update("a b c d")
# m.update("a b a b")
# random.seed(2)
# print(m.random_text(15))
print('*'*100)
# No random seeds, so your results may vary
# m = create_ngram_model(1, "frankenstein.txt")
# print(m.random_text(15))
# print('*'*100)
# m = create_ngram_model(2, "frankenstein.txt")
# print(m.random_text(15))
# print('*'*100)
# m = create_ngram_model(3, "frankenstein.txt")
# print(m.random_text(15))
# print('*'*100)
# m = create_ngram_model(4, "frankenstein.txt")
# print(m.random_text(15))
print('*'*100)
m = NgramModel(1)
m.update("a b c d")
m.update("a b a b")
print(m.perplexity("a b"))
print('*'*100)
m = NgramModel(2)
m.update("a b c d")
m.update("a b a b")
print(m.perplexity("a b"))
print('*'*100)
